# Backend Configuration
BACKEND_URL=https://staging-backend-app.agentq.id

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Frontend Environment Variables

# Backend API URL
VITE_BACKEND_URL=https://staging-backend-app.agentq.id
AGENTQ_API_URL=https://staging-backend-app.agentq.id

# AI Service URL
VITE_AI_SERVICE_URL=https://staging-ai-service.agentq.id

# WebSocket Service Configuration
PORT=3021

# LLM Configuration
LLM_PROVIDER=GEMINI
GEMINI_API_KEY=AIzaSyDjHPbanv4-BMUPU_BxUayBS1B3N0M_H40
GEMINI_MODEL=gemini-1.5-flash
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini

# Core Service URL (for API key validation)
CORE_SERVICE_URL=http://host.docker.internal:3000

#google storage
GCP_PROJECT_ID=orbital-nirvana-447600-j8
GCP_CLIENT_EMAIL=<EMAIL>
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GCP_BUCKET_NAME=agentq

ENABLE_CLOUD_STORAGE=true

# AgentQ Library Authentication
AGENTQ_AUTH_EMAIL=<EMAIL>
AGENTQ_AUTH_PASSWORD=<EMAIL>
AGENTQ_PROJECT_ID=5861f260-f05f-4649-a421-2ac264a0b1d4

# JWT Token for AgentQ Library (will be generated dynamically)
AGENTQ_JWT_TOKEN=

# AgentQ Configuration for development
AGENTQ_SERVICE_URL=wss://staging-websocket-ai-single-test
NODE_ENV=staging

# Redis Configuration for BullMQ
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=Asdqwe123@
REDIS_DB=2