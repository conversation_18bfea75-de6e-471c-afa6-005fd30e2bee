import { expect } from '@playwright/test';
import { q, test } from 'agentq_web_automation_test';

// Load test data from environment
const testDataEnv = process.env.TEST_DATA;
const clientId = process.env.CLIENT_ID;
const testCaseId = process.env.TEST_CASE_ID;
let stepsData: any = null;

if (testDataEnv) {
  try {
    stepsData = JSON.parse(testDataEnv);
    console.log(`Loaded test data for: ${stepsData.testCase.title}`);
  } catch (error) {
    console.error('Failed to parse test data:', error);
  }
}

// Use unique identifier instead of test title to prevent data mixing
const uniqueTestName = clientId ? `Test-${clientId}` : (testCaseId ? `TestCase-${testCaseId}` : 'Dynamic Test Case');

test(uniqueTestName, async ({ page }) => {
  console.log('🚀 Starting native test execution');

  if (!stepsData || !stepsData.steps) {
    console.log('⚠️ No test data provided, running simple demo test');
    // Run a simple demo test
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example/);
    console.log('✅ Demo test completed successfully');
    return;
  }

  try {
    console.log(`📋 Executing ${stepsData.steps.length} test steps`);

    // Execute each step
    for (const step of stepsData.steps) {
      if (step.action === 'prompt' && step.value) {
        console.log(`Step: prompt ${step.value} ✓`);
        await q(step.value);
      } else if (step.action === 'Go to Page' && step.target) {
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'goto' && step.target) {
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'navigate' && step.target) {
        console.log(`Step: navigate ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'Fill' && step.target && step.value) {
        console.log(`Step: fill ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
      } else if (step.action === 'write' && step.target && step.value) {
        console.log(`Step: write ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
      } else if (step.action === 'Click' && step.target) {
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
      } else if (step.action === 'click' && step.target) {
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
      } else if (step.action === 'assertText' && step.target && step.value) {
        console.log(`Step: assertText ${step.target} ${step.value} ✓`);
        await expect(page.locator(step.target)).toHaveText(step.value);
      } else {
        console.log(`Step: ${step.action} - Skipped (unsupported action)`);
      }
    }

    console.log('✅ All test steps completed successfully');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
});
