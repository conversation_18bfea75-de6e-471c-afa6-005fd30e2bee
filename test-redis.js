const Redis = require('ioredis');
require('dotenv').config();

console.log('Testing Redis connection...');
console.log('REDIS_HOST:', process.env.REDIS_HOST);
console.log('REDIS_PORT:', process.env.REDIS_PORT);
console.log('REDIS_PASSWORD:', process.env.REDIS_PASSWORD ? '***' : 'undefined');
console.log('REDIS_DB:', process.env.REDIS_DB);

const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '1'),
  maxRetriesPerRequest: null,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

console.log('Redis config:', {
  ...redisConfig,
  password: redisConfig.password ? '***' : 'undefined'
});

const redis = new Redis(redisConfig);

redis.on('connect', () => {
  console.log('✅ Connected to Redis');
});

redis.on('ready', () => {
  console.log('✅ Redis is ready');
});

redis.on('error', (err) => {
  console.error('❌ Redis error:', err.message);
});

async function testRedis() {
  try {
    await redis.connect();
    console.log('✅ Manual connect successful');
    
    const result = await redis.set('test-key', 'test-value');
    console.log('✅ SET result:', result);
    
    const value = await redis.get('test-key');
    console.log('✅ GET result:', value);
    
    await redis.del('test-key');
    console.log('✅ DEL successful');
    
    await redis.quit();
    console.log('✅ Connection closed');
    
  } catch (error) {
    console.error('❌ Redis test failed:', error.message);
    process.exit(1);
  }
}

testRedis();
