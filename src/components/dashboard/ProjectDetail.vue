<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { Project } from '../../types';

const route = useRoute();
const router = useRouter();
const API_URL = `${(import.meta as any).env.VITE_BACKEND_URL}/projects`;

const project = ref<Project | null>(null);
const loading = ref(false);
const error = ref('');

const fetchProject = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    const response = await axios.get(`${API_URL}/${route.params.id}`);
    project.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch project details';
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  if (route.name === 'test-automation') {
    router.push({
      name: 'project-test-cases',
      params: { id: route.params.id }
    });
  } else {
    router.push('/projects');
  }
};

onMounted(() => {
  fetchProject();
});
</script>

<template>
  <div class="project-detail">
    <div v-if="!(($route.name as string).includes('project-test-run-detail'))" class="header">
      <button class="back-button" @click="goBack">
        {{ $route.name === 'test-automation' ? '← Back to Test Cases' : '← Back to Projects' }}
      </button>
      <h2 v-if="project">Automate Test Case</h2>
    </div>

    <div v-if="loading" class="loading-state">
      Loading project details...
    </div>

    <div v-else-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-else-if="project" class="project-content">
      <div v-if="!(($route.name as string).includes('project-test-run-detail'))" class="project-info">
        <p class="description">{{ project.description }}</p>
        <p class="created-at">
          {{ project.createdBy ? `Created by ${project.createdBy} on ${new Date(project.createdAt).toLocaleDateString()}` : `Created on ${new Date(project.createdAt).toLocaleDateString()}` }}
        </p>
      </div>

      <div class="content-area">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.project-detail {
  padding: 5px;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;

  h2 {
    margin: 0;
    font-size: 24px;
    color: #374151;
  }
}

.back-button {
  padding: 8px 16px;
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background-color: #f9fafb;
  }
}

.loading-state {
  text-align: center;
  padding: 20px;
  color: #6b7280;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.project-content {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .project-info {
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;

    .description {
      color: #6b7280;
      margin-bottom: 12px;
    }

    .created-at {
      font-size: 14px;
      color: #9ca3af;
    }
  }
}
</style>
