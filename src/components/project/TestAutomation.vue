<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import { TestCase } from '../../types';
import { TestWebSocketClient, type TestExecutionRequest } from '../../utils/testWebSocketClient';

const route = useRoute();
const projectId = route.params.id as string;
const tcId = route.params.tcId as string;
const selectedTestCase = ref<TestCase | null>(null);
const automationSteps = ref<any[]>([]);
const isEditing = ref(false);
const isRunning = ref(false);

// Test execution state
const testRunning = ref(false);
const testProgress = ref({
  status: 'idle',
  logs: [] as string[],
  error: '',
  message: '',
  currentStep: 0,
  totalSteps: 0
});

// WebSocket client for test execution
let wsClient: TestWebSocketClient | null = null;

// Current client ID for this test session
let currentClientId: string | null = null;

// Generate unique client ID for this test session
const generateUniqueClientId = () => {
  return `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// AgentQ API key state
const agentqApiKey = ref<string | null>(null);

// Queue status state
const queueStatus = ref({
  active: 0,
  waiting: 0,
  total: 0,
  lastChecked: null as Date | null
});

// Create a set to track unique log messages
const loggedMessages = new Set();

// Function to clean log output for display
const cleanLogForDisplay = (log: string): string => {
  if (!log) return '';

  // Strip ANSI color codes and control characters
  let cleaned = log.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');

  // Remove progress indicators like [1A[2K
  cleaned = cleaned.replace(/\[\d+[A-Z]\[\d+[A-Z]/g, '');

  // Filter out JWT token verification logs that users don't need to see
  if (cleaned.includes('Verifying JWT token with:') ||
      cleaned.includes('JWT token verification successful')) {
    return '';
  }

  // For error messages with "Timed out", extract the important parts
  if (cleaned.includes('Timed out') && cleaned.includes('expect(') && cleaned.includes('Expected string:') && cleaned.includes('Received string:')) {
    try {
      // Extract the key information
      const locatorMatch = cleaned.match(/Locator: locator\('([^']+)'\)/);
      const expectedMatch = cleaned.match(/Expected string: "([^"]+)"/);
      const receivedMatch = cleaned.match(/Received string: "([^"]+)"/);

      if (locatorMatch && expectedMatch && receivedMatch) {
        return `Test failed: Element "${locatorMatch[1]}" has incorrect text.\n` +
               `Expected: "${expectedMatch[1]}"\n` +
               `Actual: "${receivedMatch[1]}"`;
      }
    } catch (e) {
      console.error('Error parsing error message:', e);
    }
  }

  // Remove internal object details
  if (cleaned.includes('matcherResult:') || cleaned.includes('Symbol(step)')) {
    cleaned = cleaned.split('matcherResult:')[0];
  }

  // Remove stack traces
  if (cleaned.includes('at /Users/')) {
    cleaned = cleaned.split('at /Users/')[0];
  }

  // Format the error message for better readability
  if (cleaned.includes('Timed out') && cleaned.includes('expect(')) {
    // Split by newlines and filter out noise
    const lines = cleaned.split('\n').filter(line =>
      line.trim() !== '' &&
      !line.includes('matcherResult') &&
      !line.includes('Symbol') &&
      !line.includes('stepId:') &&
      !line.includes('at Object.') &&
      !line.includes('at processTicksAndRejections')
    );

    // Join the first few meaningful lines
    cleaned = lines.slice(0, 5).join('\n');
  }

  return cleaned.trim();
};

// Modified addLogEntry function to clean logs before adding
const addLogEntry = (message: string) => {
  const cleanedMessage = cleanLogForDisplay(message);
  if (!cleanedMessage) return;
  
  if (!loggedMessages.has(cleanedMessage)) {
    loggedMessages.add(cleanedMessage);
    testProgress.value.logs.push(cleanedMessage);
  }
};

// Function to parse steps from the test case response
const parseStepsFromTestCase = (testCase: TestCase) => {
  const steps: any[] = [];
  
  // Add precondition as the first step if it exists
  if (testCase.precondition) {
    steps.push({
      step: 1,
      stepName: testCase.precondition.trim()
    });
  }
  
  // Add the rest of the steps
  if (testCase.steps) {
    // Map each line to an automation step
    testCase.steps.split('\n').forEach(step => {
      // Default values
      const stepObj: any = {
        step: steps.length + 1, // Start numbering after the precondition step
        stepName: step.trim()
      };
      
      steps.push(stepObj);
    });
  }
  
  // Add expectation as the last step if it exists
  if (testCase.expectation) {
    const stepObj: any = {
      step: steps.length + 1,
      stepName: testCase.expectation.trim()
    };
    
    steps.push(stepObj);
  }
  
  return steps;
};

// Add activeTab state to track which tab is active
const activeTab = ref('logs');

// Function to change the active tab
const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

const runTest = async () => {
  if (testRunning.value || testProgress.value.status === 'queued') {
    // Already running or queued a test, show a message
    const message = testProgress.value.status === 'queued'
      ? '⚠️ A test is already queued. Please wait for it to complete.'
      : '⚠️ A test is already running. Please wait for it to complete.';
    testProgress.value.logs.push(message);
    return;
  }

  // Check BullMQ for active jobs before starting
  testProgress.value.logs.push('🔍 Checking for active test jobs...');
  const queueStats = await checkQueueStatus();

  if (queueStats && queueStats.active >= 10) {
    const message = `⚠️ Cannot start test: ${queueStats.active} test(s) currently running. Maximum concurrent limit (10) reached.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push(`📊 Queue status: ${queueStats.waiting} waiting, ${queueStats.active} active`);
    testProgress.value.logs.push('🚫 Test execution blocked due to concurrent test limit.');
    return;
  }

  // Check if this specific test case is already running
  const isTestCaseRunning = await checkTestCaseRunning(selectedTestCase.value?.id ?? '');
  if (isTestCaseRunning) {
    const message = `⚠️ Cannot start test: This test case is already running from another session.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push('🚫 Test execution blocked to prevent concurrent execution of the same test case.');
    return;
  }

  if (queueStats && queueStats.waiting > 0) {
    testProgress.value.logs.push(`📋 ${queueStats.waiting} test(s) in queue. Your test will be queued.`);
  } else {
    testProgress.value.logs.push('✅ No active jobs found. Starting test...');
  }

  // Reset state for new test run
  testRunning.value = true;
  isRunning.value = true;
  testProgress.value = {
    status: 'running',
    message: 'Starting test...',
    logs: [],
    error: '',
    currentStep: 0,
    totalSteps: automationSteps.value.length
  };

  try {
    // Fetch AgentQ API key from backend
    testProgress.value.logs.push('🔑 Fetching AgentQ API key...');
    const fetchedApiKey = await fetchAgentQApiKey();

    if (!fetchedApiKey) {
      testRunning.value = false;
      isRunning.value = false;
      testProgress.value.status = 'failed';
      testProgress.value.error = 'No AgentQ API key available';
      testProgress.value.logs.push('❌ Cannot proceed without AgentQ API key');
      return;
    }

    // Get user JWT token from localStorage for backend authentication
    const userJwtToken = localStorage.getItem('token');
    console.log('JWT Token from localStorage:', userJwtToken ? 'Found' : 'Not found');
    console.log('JWT Token length:', userJwtToken ? userJwtToken.length : 0);
    if (userJwtToken) {
      testProgress.value.logs.push('🔐 Sending user JWT token to WebSocket for backend authentication');
    } else {
      testProgress.value.logs.push('⚠️ No user JWT token found - test may fail');
    }

    const wsUrl = (import.meta as any).env.VITE_WEBSOCKET_URL || 'ws://localhost:3021';
    // testProgress.value.logs.push('🔗 Connecting to WebSocket server...');

    // Generate unique client ID for this test run to prevent interference
    currentClientId = generateUniqueClientId();
    testProgress.value.logs.push(`🆔 Generated unique client ID: ${currentClientId}`);

    // Create a new WebSocket client for each test run with unique client ID
    wsClient = new TestWebSocketClient(fetchedApiKey, wsUrl, userJwtToken || undefined, currentClientId);

    // Set up event handlers
    wsClient.onConnected = () => {
      // testProgress.value.logs.push('✅ Connected to WebSocket server');
    };

    wsClient.onDisconnected = () => {
      console.log('⚠️ WebSocket disconnected, starting polling for test completion');
      // Start polling for test completion if test is still running
      if (testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
        testProgress.value.logs.push('⚠️ Connection lost, monitoring test completion...');
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }
    };

    wsClient.onTestStart = (data) => {
      console.log('Test started:', data);
      testProgress.value.status = 'running';
      testProgress.value.message = 'Test execution started';
      testProgress.value.logs.push('🚀 Test execution started');
    };

    wsClient.onTestOutput = (data) => {
      if (data.output) {
        // Clean and add the log entry
        addLogEntry(data.output);
        console.log('Test output:', data.output);
      }
    };

    wsClient.onTestComplete = (data) => {
      console.log('🎯 Frontend onTestComplete handler called with data:', data);

      // Stop polling since we received the completion message
      stopTestCompletionPolling();

      // Immediately update status to reflect final result
      const finalStatus = data.status === 'passed' ? 'completed' : 'failed';
      console.log(`🎯 Setting testProgress.status to: ${finalStatus}`);
      testProgress.value.status = finalStatus;
      testProgress.value.message = data.status === 'passed' ? 'Test completed successfully' : 'Test failed';

      // Add completion message
      const completionMessage = data.status === 'passed' ? '✅ Test completed successfully' : '❌ Test failed';
      testProgress.value.logs.push(completionMessage);

      if (data.message) {
        testProgress.value.logs.push(data.message);
      }

      // Immediately stop running state
      console.log('🎯 Setting testRunning and isRunning to false');
      testRunning.value = false;
      isRunning.value = false;

      console.log(`🎯 Test final status: ${finalStatus}, testRunning: ${testRunning.value}, testProgress.status: ${testProgress.value.status}`);

      // Reload test results to show the latest execution
      setTimeout(async () => {
        await loadHistoricalResults();
        console.log('🔄 Test results reloaded after WebSocket completion');
      }, 1000);
    };

    wsClient.onTestError = (data) => {
      console.error('Test error:', data);

      // Stop polling since we received an error
      stopTestCompletionPolling();

      testProgress.value.status = 'failed';
      testProgress.value.message = 'Test execution failed';

      // Clean the error message
      const cleanedError = cleanLogForDisplay(data.message || 'Unknown error');
      addLogEntry(`❌ Error: ${cleanedError}`);

      // Explicitly set testRunning to false
      testRunning.value = false;
      isRunning.value = false;
    };

    // Add queue-related event handlers
    wsClient.onTestQueued = (data) => {
      console.log('Test queued:', data);
      testProgress.value.status = 'queued';
      testProgress.value.message = data.message || 'Test queued successfully';

      if (data.position && data.position > 1) {
        testProgress.value.logs.push(`📋 Test queued at position ${data.position}`);
        testProgress.value.logs.push(`⏳ ${data.queueStats?.active || 0} test(s) currently running`);
      } else {
        testProgress.value.logs.push('📋 Test queued and will start shortly');
      }

      // Start polling immediately when test is queued as a backup mechanism
      console.log('🔄 Starting backup polling for queued test');
      setTimeout(() => {
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }, 2000); // Start polling after 2 seconds to give WebSocket a chance
    };

    wsClient.onQueueStatus = (data) => {
      console.log('Queue status update:', data);
      if (data.status === 'active') {
        testProgress.value.status = 'running';
        testProgress.value.message = 'Test is now running';
        testProgress.value.logs.push('🚀 Your test is now starting...');
      } else if (data.status === 'waiting') {
        testProgress.value.logs.push('⏳ Still waiting in queue...');
      } else if (data.status === 'completed') {
        // Handle completion via queue status
        console.log('🎯 Test completed via queue status');
        testProgress.value.status = 'completed';
        testProgress.value.message = 'Test completed successfully';
        testProgress.value.logs.push('✅ Test completed successfully');
        testRunning.value = false;
        isRunning.value = false;

        // Reload test results
        setTimeout(async () => {
          await loadHistoricalResults();
          console.log('🔄 Test results reloaded after queue completion');
        }, 1000);
      } else if (data.status === 'failed') {
        // Handle failure via queue status
        console.log('🎯 Test failed via queue status');
        testProgress.value.status = 'failed';
        testProgress.value.message = 'Test failed';
        testProgress.value.logs.push('❌ Test failed');
        if (data.message) {
          testProgress.value.logs.push(`❌ Error: ${data.message}`);
        }
        testRunning.value = false;
        isRunning.value = false;

        // Reload test results even for failed tests
        setTimeout(async () => {
          await loadHistoricalResults();
          console.log('🔄 Test results reloaded after queue failure');
        }, 1000);
      }
    };

    wsClient.onError = (error) => {
      console.error('WebSocket error:', error);

      // Don't immediately fail if it's a connection timeout - the queue system should handle this
      if (error.includes('Connection timeout') || error.includes('Unable to establish connection')) {
        testProgress.value.status = 'queued';
        testProgress.value.message = 'Connecting to test server...';
        testProgress.value.logs.push(`⏳ ${error}`);
        testProgress.value.logs.push('🔄 Retrying connection... Your test will be queued when connection is established.');
      } else {
        testProgress.value.status = 'failed';
        testProgress.value.message = 'WebSocket error';
        testProgress.value.logs.push(`❌ Error: ${error}`);

        // Only set testRunning to false for actual failures, not connection issues
        testRunning.value = false;
        isRunning.value = false;
      }
    };

    // Wait a moment for WebSocket connection to establish
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Prepare test execution request
    const testExecutionRequest: TestExecutionRequest = {
      testCaseId: selectedTestCase.value?.id ?? '',
      tcId: tcId,
      projectId: projectId, // Add the projectId from the route
      steps: automationSteps.value,
      testCase: {
        title: selectedTestCase.value?.title ?? '',
        precondition: selectedTestCase.value?.precondition,
        expectation: selectedTestCase.value?.expectation,
        projectId: projectId // Also include it in the testCase object
      }
    };

    // Execute the test
    wsClient.executeTest(testExecutionRequest);

  } catch (error) {
    console.error('Failed to start test execution:', error);
    testRunning.value = false;
    testProgress.value.status = 'failed';
    testProgress.value.error = error instanceof Error ? error.message : 'Unknown error';
    testProgress.value.logs.push(`❌ Failed to start test: ${testProgress.value.error}`);
  }
};

const clearLogs = () => {
  testProgress.value.logs = [];
  testProgress.value.error = '';
};

// Function to check queue status
const checkQueueStatus = async () => {
  try {
    // Use WebSocket server URL (port 3021) for queue stats, not the backend URL (port 3010)
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3021';
    const response = await axios.get(`${wsServerUrl}/api/queue/stats`);

    if (response.data && response.data.success) {
      queueStatus.value = {
        ...response.data.data,
        lastChecked: new Date()
      };
      return response.data.data;
    }
  } catch (error) {
    console.error('Failed to check queue status:', error);
  }
  return null;
};

// Function to check if a specific test case is already running
const checkTestCaseRunning = async (testCaseId: string) => {
  try {
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3021';
    const response = await axios.get(`${wsServerUrl}/api/queue/running-test-cases`);

    if (response.data && response.data.success) {
      const runningTestCases = response.data.data || [];
      return runningTestCases.includes(testCaseId);
    }
  } catch (error) {
    console.error('Failed to check running test cases:', error);
  }
  return false;
};

// Function to force stop a running test
const forceStopTest = () => {
  console.log('🛑 Force stopping test execution');
  testProgress.value.logs.push('🛑 Test execution manually stopped');
  testProgress.value.status = 'failed';
  testProgress.value.message = 'Test execution stopped by user';
  testRunning.value = false;
  isRunning.value = false;

  // Stop polling
  stopTestCompletionPolling();

  // Disconnect WebSocket if connected
  if (wsClient) {
    wsClient.disconnect();
    wsClient = null;
  }

  // Reload test results in case there were any completed before stopping
  setTimeout(async () => {
    await loadHistoricalResults();
    console.log('🔄 Test results reloaded after manual stop');
  }, 500);
};

// Polling mechanism to check test completion when WebSocket is disconnected
let testCompletionPoller: NodeJS.Timeout | null = null;
let pollingStartTime: number | null = null;
const POLLING_TIMEOUT = 15 * 60 * 1000; // 15 minutes timeout

const startTestCompletionPolling = (clientId: string) => {
  // Clear any existing poller
  if (testCompletionPoller) {
    clearInterval(testCompletionPoller);
  }

  console.log('🔄 Starting test completion polling for client:', clientId);
  testProgress.value.logs.push('🔄 Starting background monitoring for test completion...');

  pollingStartTime = Date.now();

  testCompletionPoller = setInterval(async () => {
    // Check for timeout
    if (pollingStartTime && Date.now() - pollingStartTime > POLLING_TIMEOUT) {
      console.log('⏰ Polling timeout reached, forcing completion');
      testProgress.value.logs.push('⏰ Test monitoring timeout reached - forcing completion');
      testProgress.value.status = 'failed';
      testProgress.value.message = 'Test execution timed out';
      testRunning.value = false;
      isRunning.value = false;
      stopTestCompletionPolling();
      return;
    }

    // Only poll if test is still running or queued
    if (testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
      try {
        console.log('🔍 Polling for test completion...', {
          status: testProgress.value.status,
          clientId: clientId,
          currentClientId: currentClientId
        });

        // First, check BullMQ queue status to see if there are any active jobs
        const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3021';
        const queueResponse = await axios.get(`${wsServerUrl}/api/queue/stats`);

        if (queueResponse.data && queueResponse.data.success) {
          const queueStats = queueResponse.data.data;
          console.log('🔍 Queue stats:', queueStats);

          // If no active jobs and we're still in queued/running state, the test likely completed
          if (queueStats.active === 0 && (testProgress.value.status === 'running' || testProgress.value.status === 'queued')) {
            console.log('🔍 No active jobs but test still running/queued - test likely completed, checking for results');
          } else if (queueStats.active > 0) {
            console.log('🔍 Jobs still active in queue, continuing to wait...');
          }
        }

        // Check if there are any recent test results for this test case
        const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
        const testCaseId = selectedTestCase.value?.id;
        const apiUrl = `${backendUrl}/temp-test-results/test-case/${testCaseId}`;

        console.log('🔍 Polling API details:', {
          backendUrl,
          testCaseId,
          apiUrl,
          hasSelectedTestCase: !!selectedTestCase.value
        });

        const response = await axios.get(apiUrl);

        if (response.data && response.data.length > 0) {
          // Sort by execution date to get the most recent execution
          const sortedResults = response.data.sort((a: any, b: any) => {
            const aTime = new Date(a.executedAt || a.createdAt).getTime();
            const bTime = new Date(b.executedAt || b.createdAt).getTime();
            return bTime - aTime;
          });
          const latestResult = sortedResults[0];

          // Use executedAt if available, otherwise fall back to createdAt
          const resultTime = new Date(latestResult.executedAt || latestResult.createdAt);
          const testStartTime = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago

          console.log('🔍 Latest result:', {
            id: latestResult.id,
            status: latestResult.status,
            createdAt: latestResult.createdAt,
            executedAt: latestResult.executedAt,
            resultTime: resultTime.toISOString(),
            testStartTime: testStartTime.toISOString(),
            isRecent: resultTime > testStartTime
          });

          // Check if this result is for the current test session
          // If no active jobs in queue AND we have a test result, complete the test
          const hasNoActiveJobs = queueResponse.data?.success && queueResponse.data.data?.active === 0;
          const shouldComplete = hasNoActiveJobs || (resultTime > testStartTime);

          if (shouldComplete) {
            console.log('🎯 Found test result and no active jobs - completing test', {
              hasNoActiveJobs,
              resultTime: resultTime.toISOString(),
              isRecent: resultTime > testStartTime
            });
            testProgress.value.logs.push('🎯 Found completed test execution via polling');

            // Update the frontend status based on the test result (not job status)
            // The job completed successfully, but the test scenario may have passed or failed
            const finalStatus = latestResult.status === 'passed' ? 'completed' : 'failed';
            testProgress.value.status = finalStatus;
            testProgress.value.message = latestResult.status === 'passed' ? 'Test completed successfully' : 'Test failed';

            const completionMessage = latestResult.status === 'passed' ? '✅ Test completed successfully' : '❌ Test failed';
            testProgress.value.logs.push(completionMessage);
            testProgress.value.logs.push('📊 Status updated via polling (WebSocket connection was lost)');

            // Add error details if test failed
            if (latestResult.status === 'failed' && latestResult.errorMessage) {
              const cleanedError = cleanLogForDisplay(latestResult.errorMessage);
              if (cleanedError) {
                testProgress.value.logs.push(`❌ Error: ${cleanedError}`);
              }
            }

            // Stop running state - the job execution is complete regardless of test outcome
            testRunning.value = false;
            isRunning.value = false;

            // Stop polling
            stopTestCompletionPolling();

            // Reload test results to show the latest execution
            setTimeout(async () => {
              await loadHistoricalResults();
              console.log('🔄 Test results reloaded after completion');
            }, 1000); // Small delay to ensure backend has processed the result
          } else {
            console.log('🔍 No recent test results found, continuing to poll...');
          }
        } else {
          console.log('🔍 No test results found, continuing to poll...');

          // If no active jobs in queue and no test results, something went wrong
          // Force completion to prevent infinite running state
          if (queueResponse.data?.success && queueResponse.data.data?.active === 0) {
            console.log('🚨 No active jobs and no test results - forcing completion to prevent stuck state');
            testProgress.value.logs.push('⚠️ Test execution completed but no results found');
            testProgress.value.status = 'failed';
            testProgress.value.message = 'Test completed but results not found';
            testRunning.value = false;
            isRunning.value = false;
            stopTestCompletionPolling();
          }
        }
      } catch (error) {
        console.error('Error polling for test completion:', error);

        // Add detailed error logging
        if (axios.isAxiosError(error)) {
          console.error('🔍 Axios error details:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url,
            message: error.message
          });
          testProgress.value.logs.push(`⚠️ API Error: ${error.response?.status || error.message}`);
        } else {
          console.error('🔍 Non-axios error:', error);
          testProgress.value.logs.push('⚠️ Error checking test status, retrying...');
        }
      }
    } else {
      // Test is no longer running, stop polling
      console.log('🛑 Test no longer running, stopping polling');
      stopTestCompletionPolling();
    }
  }, 3000); // Poll every 3 seconds (more frequent)
};

const stopTestCompletionPolling = () => {
  if (testCompletionPoller) {
    console.log('🛑 Stopping test completion polling');
    clearInterval(testCompletionPoller);
    testCompletionPoller = null;
    pollingStartTime = null;
  }
};

// Function to fetch AgentQ API key from backend
const fetchAgentQApiKey = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`
    );

    if (response.data && response.data.length > 0) {
      // Find the AgentQ API key
      const agentqKey = response.data.find((key: any) => key.provider === 'agentq');
      if (agentqKey) {
        agentqApiKey.value = agentqKey.apiKey;
        console.log('AgentQ API key fetched successfully');
        return agentqKey.apiKey;
      } else {
        console.error('No AgentQ API key found');
        testProgress.value.logs.push('⚠️ No AgentQ API key found in backend');
        return null;
      }
    } else {
      console.error('No API keys found');
      testProgress.value.logs.push('⚠️ No API keys configured in backend');
      return null;
    }
  } catch (error) {
    console.error('Failed to fetch AgentQ API key:', error);
    testProgress.value.logs.push('❌ Failed to fetch AgentQ API key from backend');
    return null;
  }
};

const editTest = () => {
  isEditing.value = true;
  
  // For each step, initialize UI state based on existing data
  automationSteps.value.forEach(step => {
    // Initialize with existing values or empty strings
    step.target = step.target || '';
    step.value = step.value || '';
    
    // Convert existing prompts to the "prompt" action type
    if (step.prompt && !step.action) {
      step.action = 'prompt';
      step.value = step.prompt;
      step.prompt = '';
    }
    
    // Initialize dropdown states (all closed by default)
    step.showInteractionDropdown = false;
    step.showAssertionDropdown = false;
  });
};

const saveTest = async () => {
  try {
    // Convert "prompt" actions back to the prompt field for storage
    automationSteps.value.forEach(step => {
      if (step.action === 'prompt') {
        step.prompt = step.value;
        // Don't clear the action here, as we want to keep it for display
      }
    });
    
    // First update the test case details if they've changed
    if (selectedTestCase.value) {
      const updatedTestCase = {
        title: selectedTestCase.value.title,
        precondition: selectedTestCase.value.precondition,
        steps: selectedTestCase.value.steps,
        expectation: selectedTestCase.value.expectation
      };
      
      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${selectedTestCase.value.id}`,
        updatedTestCase
      );
    }
    
    // Then save the automation steps
    const automationData = {
      testCaseId: selectedTestCase.value?.id,
      steps: automationSteps.value
    };
    
    await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation`,
      automationData
    );
    console.log('Test automation steps saved successfully');
    
    // edit test case type to automated
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/type`,
      { testCaseType: 'automation' }
    );
    console.log('Test case type updated to automated');

    // edit automated by agentq to true
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation-by-agentq`,
      { automationByAgentq: true }
    );
    console.log('Test case automation by AgentQ updated to true');

    isEditing.value = false;
  } catch (error) {
    console.error('Failed to save test:', error);
  }
};

const cancelEdit = () => {
  isEditing.value = false;
  // Reload the original test case to discard changes
  fetchTestCaseDetails();
};

const fetchAutomationSteps = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation`
    );
    
    if (response.data && response.data.steps && response.data.steps.length > 0) {
      // Use the saved automation steps
      automationSteps.value = response.data.steps;
      console.log('Loaded saved automation steps:', automationSteps.value);
    } else if (selectedTestCase.value) {
      // Generate steps from the test case if no saved automation exists
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
      console.log('Generated automation steps from test case:', automationSteps.value);
    }
  } catch (error) {
    console.error('Failed to fetch automation steps:', error);
    // Fall back to generating steps from the test case
    if (selectedTestCase.value) {
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
    }
  }
};

const fetchTestCaseDetails = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}`
    );
    
    if (response.data) {
      selectedTestCase.value = response.data;
      console.log('Found test case:', response.data);
      
      // Fetch automation steps after getting the test case
      await fetchAutomationSteps();
    } else {
      console.error('Test case not found with tcId:', tcId);
    }
  } catch (error) {
    console.error('Failed to fetch test case details:', error);
  }
};

// Load test case details and API key on mount
onMounted(async () => {
  await fetchTestCaseDetails();
  await fetchAgentQApiKey();
  // Load historical test results if available
  await loadHistoricalResults();
  // Check queue status initially
  await checkQueueStatus();

  // Set up periodic queue status checking (every 5 seconds)
  const queueCheckInterval = setInterval(async () => {
    await checkQueueStatus();
  }, 5000);

  // Store interval ID for cleanup
  (window as any).queueCheckInterval = queueCheckInterval;
});

// Cleanup WebSocket connection on unmount
onUnmounted(() => {
  if (wsClient) {
    wsClient.disconnect();
    wsClient = null;
  }

  // Clean up test completion polling
  stopTestCompletionPolling();

  // Clean up queue check interval
  if ((window as any).queueCheckInterval) {
    clearInterval((window as any).queueCheckInterval);
    (window as any).queueCheckInterval = null;
  }
});

const toggleInteractionDropdown = (step: any) => {
  // Close all dropdowns first
  automationSteps.value.forEach(s => {
    if (s !== step) {
      s.showInteractionDropdown = false;
    }
    s.showAssertionDropdown = false;
  });
  
  // Toggle the dropdown for this step
  step.showInteractionDropdown = !step.showInteractionDropdown;
};

const toggleAssertionDropdown = (step: any) => {
  // Close all dropdowns first
  automationSteps.value.forEach(s => {
    if (s !== step) {
      s.showAssertionDropdown = false;
    }
    s.showInteractionDropdown = false;
  });
  
  // Toggle the dropdown for this step
  step.showAssertionDropdown = !step.showAssertionDropdown;
};

const setStepAction = (step: any, action: string) => {
  step.action = action;
  
  // Close dropdowns
  step.showInteractionDropdown = false;
  step.showAssertionDropdown = false;
  
  // Hide prompt field when setting an action
  step.showPromptField = false;
  
  // Set default values based on action
  if (action === 'goto') {
    step.target = '';
    step.value = '';
  } else if (action === 'click') {
    step.target = '';
    step.value = '';
  } else if (action === 'write') {
    step.target = '';
    step.value = '';
  } else if (action === 'assertText') {
    step.target = '';
    step.value = '';
  } else if (action === 'assertUrl') {
    step.target = '';
    step.value = '';
  } else if (action === 'assertVisible') {
    step.target = '';
    step.value = '';
  }
};

const togglePromptField = (step: any) => {
  // Set the action to "prompt"
  step.action = 'prompt';
  
  // Initialize the prompt value if it doesn't exist
  if (!step.value) {
    step.value = step.prompt || ''; // Use existing prompt if available
    step.prompt = ''; // Clear the old prompt field
  }
  
  // Close other dropdowns
  step.showInteractionDropdown = false;
  step.showAssertionDropdown = false;
  
  // Close dropdowns for other steps
  automationSteps.value.forEach(s => {
    if (s !== step) {
      s.showInteractionDropdown = false;
      s.showAssertionDropdown = false;
    }
  });
};

const clearStepAction = (step: any) => {
  // Clear the action and related fields
  step.action = '';
  step.target = '';
  step.value = '';
  
  // Close all dropdowns
  step.showInteractionDropdown = false;
  step.showAssertionDropdown = false;
};

// Helper function to determine log level from log message
const getLogLevel = (log: string): string => {
  const logLower = log.toLowerCase();
  if (logLower.includes('error') || logLower.includes('failed') || logLower.includes('❌')) {
    return 'error';
  } else if (logLower.includes('warn') || logLower.includes('warning') || logLower.includes('⚠️')) {
    return 'warning';
  } else if (logLower.includes('success') || logLower.includes('✅') || logLower.includes('passed')) {
    return 'success';
  } else {
    return 'info';
  }
};

// Function to load historical test results
const loadHistoricalResults = async () => {
  try {
    if (!selectedTestCase.value?.id) {
      console.log('No test case ID available, skipping historical results load');
      return;
    }
    
    console.log(`Loading historical results for test case: ${selectedTestCase.value.id}`);
    
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/test-case/${selectedTestCase.value.id}`
    );

    if (response.data && response.data.length > 0) {
      // Load the most recent test result
      const latestResult = response.data[0];
      console.log('Found historical test result:', latestResult);
      
      // Update test status
      testProgress.value.status = latestResult.status || 'idle';
      
      // If there's a duration, display it
      if (latestResult.duration) {
        testProgress.value.message = `Test ${latestResult.status} in ${(latestResult.duration / 1000).toFixed(1)}s`;
      } else {
        testProgress.value.message = `Test ${latestResult.status}`;
      }
      
      // If there's an error message, display it
      if (latestResult.errorMessage) {
        testProgress.value.error = latestResult.errorMessage;
      }
      
      // Load logs either from the result object or fetch them separately
      if (latestResult.logs && latestResult.logs.length > 0) {
        // Clean logs before displaying them
        testProgress.value.logs = latestResult.logs.map(cleanLogForDisplay).filter(Boolean);
        console.log(`Loaded ${testProgress.value.logs.length} logs from result object`);
      } else if (latestResult.id) {
        // Try to fetch logs separately
        try {
          const logsResponse = await axios.get(
            `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/${latestResult.id}/logs`
          );
          
          if (logsResponse.data && logsResponse.data.logs) {
            // Clean logs before displaying them
            testProgress.value.logs = logsResponse.data.logs.map(cleanLogForDisplay).filter(Boolean);
            console.log(`Loaded ${testProgress.value.logs.length} logs from separate endpoint`);
          }
        } catch (logsError) {
          console.error('Failed to load logs separately:', logsError);
        }
      }
      
      // Clear the logged messages set when loading historical results
      loggedMessages.clear();
      
      // Add all loaded logs to the logged messages set to prevent duplicates
      testProgress.value.logs.forEach(log => {
        loggedMessages.add(log);
      });
    } else {
      console.log('No historical test results found');
    }
  } catch (error) {
    console.error('Failed to load historical results:', error);
  }
};

// State for video playback
const videoUrl = ref<string>('');
const loadingVideo = ref(false);

// Function to load video for the current test result
const loadVideo = async () => {
  if (activeTab.value !== 'video') return;
  
  try {
    loadingVideo.value = true;
    
    // Find the latest test result for this test case
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/test-case/${selectedTestCase.value?.id}`
    );
    
    if (response.data && response.data.length > 0) {
      const latestResult = response.data[0];
      
      if (latestResult.id) {
        // Get the signed URL for the video
        const videoResponse = await axios.get(
          `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/${latestResult.id}/video`
        );
        
        if (videoResponse.data && videoResponse.data.videoUrl) {
          videoUrl.value = videoResponse.data.videoUrl;
          console.log('Video URL loaded:', videoUrl.value);
        } else {
          videoUrl.value = '';
          console.log('No video available for this test result');
        }
      }
    } else {
      videoUrl.value = '';
    }
  } catch (error) {
    console.error('Failed to load video:', error);
    videoUrl.value = '';
  } finally {
    loadingVideo.value = false;
  }
};

// Watch for tab changes to load video when the Video tab is selected
watch(activeTab, (newTab) => {
  if (newTab === 'video') {
    loadVideo();
  } else if (newTab === 'history') {
    // loadTestHistory();
  }
});
</script>

<template>
  <div class="automation-container">
    <div class="automation-header">
      <h2>{{ selectedTestCase?.title }}</h2>
      <div class="action-buttons">
        <template v-if="!isEditing">
          <div class="api-key-status">
            <span v-if="agentqApiKey" class="status-indicator api-connected">
              <span class="icon">🔑</span> API Key Ready
            </span>
            <span v-else class="status-indicator api-disconnected">
              <span class="icon">🔑</span> No API Key
            </span>
          </div>
          <div class="queue-status" v-if="queueStatus.active > 0 || queueStatus.waiting > 0">
            <span class="status-indicator queue-busy">
              <span class="icon">📊</span> Queue: {{ queueStatus.active }} active, {{ queueStatus.waiting }} waiting
            </span>
          </div>
          <div class="status-header" v-if="!(queueStatus.active >= 10)">
            <span v-if="testProgress.status === 'queued'" class="status-indicator queued">
              <span class="icon">📋</span> Test Queued
            </span>
            <span v-else-if="testRunning" class="status-indicator running">
              <span class="icon">🟡</span> Running Test...
            </span>
            <span v-else-if="testProgress.status === 'completed'" class="status-indicator completed">
              <span class="icon">✅</span> Test Completed
            </span>
            <span v-else-if="testProgress.status === 'failed'" class="status-indicator failed">
              <span class="icon">❌</span> Test Failed
            </span>
            <span v-else class="status-indicator idle">
              <span class="icon">⚪</span> Ready
            </span>
          </div>
          <button
            class="run-button"
            @click="runTest"
            :disabled="testRunning || testProgress.status === 'queued' || queueStatus.active >= 10"
            v-if="!(queueStatus.active >= 10) && !testRunning && testProgress.status !== 'queued'"
          >
            <span class="icon">▶️</span>
            <span>Run Test</span>
          </button>
          <button
            class="stop-button"
            @click="forceStopTest"
            v-if="testRunning || testProgress.status === 'queued'"
          >
            <span class="icon">⏹️</span>
            <span>{{ testProgress.status === 'queued' ? 'Cancel' : 'Stop Test' }}</span>
          </button>
          <button class="edit-button" @click="editTest" :disabled="testRunning">
            <span class="icon">✏️</span> Edit
          </button>
        </template>
        <template v-else>
          <button class="save-button" @click="saveTest">
            <span class="icon">💾</span> Save
          </button>
          <button class="cancel-button" @click="cancelEdit">
            <span class="icon">❌</span> Cancel
          </button>
        </template>
      </div>
    </div>

    <div class="automation-content" :class="{ 'editing': isEditing }">
      <div class="steps-panel">
        <div v-for="step in automationSteps" :key="step.step" class="step-item">
          <div class="step-header">Step {{ step.step }}</div>
          <div class="step-details">
            <div v-if="step.stepName" class="step-name">{{ step.stepName }}</div>
            
            <!-- View mode - show action details -->
            <div v-if="!isEditing" class="step-action-details">
              <div v-if="step.action === 'prompt'" class="step-action">
                <strong>Action: Prompt</strong>
                <div>Value: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'goto'" class="step-action">
                <strong>Action: Go to Page</strong>
                <div>URL: {{ step.target }}</div>
              </div>
              <div v-else-if="step.action === 'click'" class="step-action">
                <strong>Action: Click</strong>
                <div>Target: {{ step.target }}</div>
              </div>
              <div v-else-if="step.action === 'write'" class="step-action">
                <strong>Action: Fill</strong>
                <div>Target: {{ step.target }}</div>
                <div>Value: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'assertText'" class="step-action">
                <strong>Action: Assert Text</strong>
                <div>Target: {{ step.target }}</div>
                <div>Expected Text: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'assertUrl'" class="step-action">
                <strong>Action: Assert URL</strong>
                <div>Expected URL: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'assertVisible'" class="step-action">
                <strong>Action: Assert Visible</strong>
                <div>Target: {{ step.target }}</div>
              </div>
            </div>

            <!-- View mode - show message if no action is set up -->
            <div v-else-if="!isEditing" class="action-message">
              <div class="no-action-message">
                The action is not set up yet
              </div>
            </div>
            
            <!-- Edit mode - show action buttons and fields -->
            <div v-else class="step-actions-container">
              <div class="step-action-buttons">
                <div class="dropdown">
                  <button class="action-button prompt-button" @click="togglePromptField(step)">
                    Add Prompt
                  </button>
                </div>
                
                <div class="dropdown">
                  <button class="action-button interaction-button" @click="toggleInteractionDropdown(step)">
                    Add Interaction
                  </button>
                  <div v-if="step.showInteractionDropdown" class="dropdown-content">
                    <div class="dropdown-item" @click="setStepAction(step, 'goto')">Go to Page</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'click')">Click</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'write')">Fill</div>
                  </div>
                </div>
                
                <div class="dropdown">
                  <button class="action-button assertion-button" @click="toggleAssertionDropdown(step)">
                    Add Assertion
                  </button>
                  <div v-if="step.showAssertionDropdown" class="dropdown-content">
                    <div class="dropdown-item" @click="setStepAction(step, 'assertText')">Assert Text</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'assertUrl')">Assert URL</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'assertVisible')">Assert Visible</div>
                  </div>
                </div>
              </div>
              
              <!-- Prompt action field -->
              <div v-if="step.action === 'prompt'" class="action-fields">
                <div class="field-header">
                  <label>Prompt:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.value" type="text" placeholder="Enter prompt for AI" />
              </div>
              
              <!-- Show additional fields based on selected action -->
              <div v-if="step.action === 'goto'" class="action-fields">
                <div class="field-header">
                  <label>URL:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter URL" />
              </div>
              
              <div v-if="step.action === 'click'" class="action-fields">
                <div class="field-header">
                  <label>Target:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter selector" />
              </div>
              
              <div v-if="step.action === 'write'" class="action-fields">
                <div class="field-header">
                  <label>Target:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter selector" />
                <div class="field">
                  <label>Value:</label>
                  <input v-model="step.value" type="text" placeholder="Enter value" />
                </div>
              </div>
              
              <div v-if="step.action === 'assertText' || step.action === 'assertVisible'" class="action-fields">
                <div class="field-header">
                  <label>Target:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter selector" />
                <div class="field">
                  <label>Expected Text:</label>
                  <input v-model="step.value" type="text" placeholder="Enter expected text" />
                </div>
              </div>
              
              <div v-if="step.action === 'assertUrl'" class="action-fields">
                <div class="field-header">
                  <label>Expected URL:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.value" type="text" placeholder="Enter expected URL" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="preview-panel" :class="{ 'isEditing': isEditing }">
        <div class="tabs">
          <div 
            class="tab" 
            :class="{ active: activeTab === 'logs' }"
            @click="setActiveTab('logs')"
          >
            <span class="tab-icon">📋</span> Logs
          </div>
          <div 
            class="tab" 
            :class="{ active: activeTab === 'video' }"
            @click="setActiveTab('video')"
          >
            <span class="tab-icon">🎬</span> Video
          </div>
        </div>
        <div class="tab-content-container">
          <!-- Logs tab content -->
          <div v-if="activeTab === 'logs'" class="tab-content">
            <div class="logs-container">
              <!-- Test execution status -->
              <div v-if="testProgress.status !== 'idle'" class="status-container">
                <div class="status-header">
                  <span class="status-badge" :class="testProgress.status">
                    {{ testProgress.status.toUpperCase() }}
                  </span>
                  <span v-if="testProgress.totalSteps > 0" class="progress-info">
                    Step {{ testProgress.currentStep }} of {{ testProgress.totalSteps }}
                  </span>
                </div>
                <div v-if="testProgress.message" class="status-message">
                  {{ testProgress.message }}
                </div>
                <div v-if="testProgress.error" class="error-message">
                  <pre>{{ testProgress.error }}</pre>
                </div>
              </div>

              

              <!-- Real-time logs from test execution -->
              <div v-for="(log, index) in testProgress.logs" :key="index" class="log-entry">
                <span class="log-time">{{ new Date().toLocaleTimeString() }}</span>
                <span class="log-level" :class="getLogLevel(log)">{{ getLogLevel(log).toUpperCase() }}</span>
                <span class="log-message">{{ log }}</span>
              </div>

              <!-- Placeholder when no logs -->
              <div v-if="testProgress.logs.length === 0 && testProgress.status === 'idle'" class="no-logs">
                <p>No test execution logs yet. Click "Run Test" to start automation testing.</p>
              </div>

              <!-- Clear logs button -->
              <div v-if="testProgress.logs.length > 0" class="logs-actions">
                <button class="clear-logs-button" @click="clearLogs()">
                  Clear Logs
                </button>
              </div>
            </div>
          </div>
          
          <!-- Video tab content -->
          <div v-if="activeTab === 'video'" class="tab-content">
            <div v-if="loadingVideo" class="loading-indicator">
              <div class="spinner"></div>
              <p>Loading video...</p>
            </div>
            
            <div v-else-if="!videoUrl" class="no-video">
              <p>No video recording available for this test run.</p>
              <p class="no-video-info">Videos are automatically recorded.</p>
            </div>
            
            <div v-else class="video-player">
              <video controls autoplay>
                <source :src="videoUrl" type="video/webm">
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.automation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.automation-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 15px;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.test-case-title {
  font-weight: bold;
  margin-left: 20px;
}

.action-buttons {
  margin-left: auto;
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  background-color: #f9fafb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.api-key-status {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.status-indicator {
  font-size: 12px;
  padding: 6px 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.status-indicator.api-connected {
  background-color: #ecfdf5;
  color: #047857;
  border: 1px solid #d1fae5;
}

.status-indicator.api-disconnected {
  background-color: #fef2f2;
  color: #b91c1c;
  border: 1px solid #fee2e2;
}

.queue-status {
  margin-left: 8px;
}

.status-indicator.queue-busy {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-indicator.queued {
  background-color: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.status-indicator.running {
  background-color: #fffbeb;
  color: #92400e;
  border: 1px solid #fef3c7;
}

.status-indicator.completed {
  background-color: #ecfdf5;
  color: #047857;
  border: 1px solid #d1fae5;
}

.status-indicator.failed {
  background-color: #fef2f2;
  color: #b91c1c;
  border: 1px solid #fee2e2;
}

.status-indicator.idle {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.run-button {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stop-button {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.edit-button {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #4b5563;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.save-button {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cancel-button {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  color: #4b5563;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.edit-button:hover, .cancel-button:hover {
  background-color: #e5e7eb;
  color: #374151;
}

.run-button:hover:not(:disabled), .save-button:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stop-button:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.run-button:disabled, .edit-button:disabled {
  background-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}



/* Main container layout */
.automation-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 180px);
  min-height: 500px;
  position: relative;
}

/* Steps panel styling with fixed width ratio */
.steps-panel {
  flex: 0 0 50%;
  width: 50%;
  max-width: 50%;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Preview panel styling with fixed width ratio */
.preview-panel {
  flex: 0 0 50%;
  width: 50%;
  max-width: 50%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 100%;
}

/* Logs container with fixed height and scrolling */
.logs-container {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: calc(100vh - 280px);
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
  background-color: #f8f9fa;
  border-radius: 4px;
}

/* Log entries with word wrapping to prevent horizontal overflow */
.log-entry {
  margin-bottom: 8px;
  padding: 6px 8px;
  background-color: white;
  border-radius: 4px;
  border-left: 3px solid #e5e7eb;
  word-break: break-word;
  white-space: pre-wrap;
}

/* Tab content container with fixed height */
.tab-content-container {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 45px); /* Subtract tab height */
}

.tab-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Video container with fixed dimensions */
.video-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

/* Make sure video maintains aspect ratio without causing layout shifts */
.video-player video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Ensure consistent styling in edit mode */
.automation-content.editing .steps-panel,
.automation-content.editing .preview-panel {
  height: 100%;
}

.step-item {
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: visible; /* Changed from hidden to visible */
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-header {
  font-weight: 600;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-details {
  padding: 16px;
  width: 100%;
  overflow: visible;
}

.step-name {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 16px;
  line-height: 1.5;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-radius: 6px;
  border-left: 3px solid #10b981;
  word-break: break-word;
}

.step-action-details {
  margin-top: 12px;
  width: 100%;
}

.step-action {
  background-color: #f3f4f6;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  width: 100%;
}

.step-action strong {
  display: block;
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.step-action div {
  margin-bottom: 6px;
  font-size: 13px;
  color: #4b5563;
  word-break: break-word;
}

.step-actions-container {
  margin-top: 16px;
  width: 100%;
}

.step-action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  width: 100%;
}

.action-button {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #4b5563;
}

.action-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.prompt-button {
  background-color: #eff6ff;
  color: #3b82f6;
  border-color: #dbeafe;
}

.prompt-button:hover {
  background-color: #dbeafe;
  color: #2563eb;
}

.interaction-button {
  background-color: #f3dbfe;
  color: #8f3bf6;
  border-color: #dbeafe;
}

.interaction-button:hover {
  background-color: #ebcbfa;
  color: #7520f4;
}

.assertion-button {
  background-color: #fef8db;
  color: #d0a01c;
  border-color: #dbeafe;
}

.assertion-button:hover {
  background-color: #fae57e;
  color: #9b7409;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 100; /* Higher z-index to ensure visibility */
  min-width: 180px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-top: 4px;
}

.dropdown-item {
  padding: 10px 16px;
  font-size: 13px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.1s ease;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.dropdown-item:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.dropdown-item:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.action-fields {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 6px;
  margin-top: 12px;
  border: 1px solid #e5e7eb;
  width: 100%;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  width: 100%;
}

.field-header label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.close-field-button {
  background: none;
  border: none;
  font-size: 18px;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-field-button:hover {
  color: #ef4444;
}

.action-fields input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.action-fields input:focus {
  outline: none;
  border-color: #4caeeb;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.field {
  margin-bottom: 12px;
  width: 100%;
}

.field label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #4b5563;
}

.field:last-child {
  margin-bottom: 0;
}

.no-action-message {
  padding: 12px;
  background-color: #f3f4f6;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  text-align: center;
  font-style: italic;
  border: 1px dashed #d1d5db;
}

.prompt-field {
  margin-top: 12px;
}

.prompt-field textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  min-height: 100px;
  resize: vertical;
  transition: all 0.2s ease;
}

.prompt-field textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1);
}

.prompt-field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.prompt-field-header label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #6b7280;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #1e1e1e;
  color: #e5e7eb;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.passed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #6b7280;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #1e1e1e;
  color: #e5e7eb;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #6b7280;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #1e1e1e;
  color: #e5e7eb;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}/* Test Status and Logs Styles */
.test-status {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.queued {
  background-color: #cce7ff;
  color: #0056b3;
}

.status-badge.running {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.progress-info {
  font-size: 14px;
  color: #6c757d;
}

.status-message {
  font-size: 14px;
  color: #495057;
  margin-bottom: 4px;
}

.error-message {
  font-size: 14px;
  color: #721c24;
  background-color: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  margin: 10px 0;
  white-space: pre-line;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
  font-size: 12px;
}

.log-level {
  min-width: 60px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 11px;
}

.log-level.info {
  color: #17a2b8;
}

.log-level.success {
  color: #28a745;
}

.log-level.warning {
  color: #ffc107;
}

.log-level.error {
  color: #dc3545;
}

.log-message {
  flex: 1;
  color: #495057;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.logs-actions {
  margin-top: 16px;
  text-align: right;
}

.clear-logs-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-button:hover {
  background-color: #5a6268;
}

.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.video-player video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
}

.no-video {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  color: #9ca3af;
  padding: 20px;
}

.no-video p {
  margin: 8px 0;
}

.no-video-info {
  font-size: 14px;
  color: #999;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.view-video-button {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 8px;
}

.view-video-button:hover {
  background-color: #218838;
}

/* History entry styles */
.history-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-logs {
  background-color: #007bff;
  color: white;
}

.view-logs:hover {
  background-color: #0069d9;
}

.view-video {
  background-color: #28a745;
  color: white;
}

.view-video:hover {
  background-color: #218838;
}

.icon {
  font-size: 14px;
}
/* Improved tab styling */
.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
  background-color: #f9fafb;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  overflow: hidden;
}

.tab {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.tab.active {
  color: #10b981;
  background-color: white;
  border-bottom: 2px solid #10b981;
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #10b981;
}
</style>
