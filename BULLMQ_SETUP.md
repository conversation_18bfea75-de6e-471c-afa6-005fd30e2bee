# BullMQ Queue System Setup

This document explains the BullMQ queue system implementation for handling concurrent test execution.

## 🎯 Problem Solved

- **Concurrent Test Conflicts**: Multiple users running tests simultaneously causing WebSocket connection errors
- **Mixed Test Results**: Test results from different users getting mixed up in Google Cloud Storage
- **Resource Management**: No control over concurrent test execution

## 🚀 Solution: BullMQ Queue System

### Features Implemented

1. **Job Queue**: Only 1 test executes at a time, others wait in queue
2. **Dashboard Monitoring**: Bull Board dashboard for queue visualization
3. **Real-time Updates**: WebSocket notifications for queue status
4. **Graceful Handling**: Proper error handling and retry mechanisms

## 📋 Prerequisites

### 1. Redis Server
Redis is required for BullMQ to work. You can run it using Docker:

```bash
# Start Redis using Docker Compose
npm run redis:start

# Check Redis logs
npm run redis:logs

# Stop Redis
npm run redis:stop
```

Or install Redis locally:
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server
```

### 2. Environment Variables
Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Required variables:
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

## 🛠 Installation & Setup

1. **Install Dependencies** (already done):
```bash
npm install bullmq @bull-board/api @bull-board/express ioredis
```

2. **Start Redis**:
```bash
npm run redis:start
```

3. **Start the Server**:
```bash
npm run dev
```

## 📊 Monitoring & Dashboard

### Bull Board Dashboard
Access the queue dashboard at: `http://localhost:3021/admin/queues`

Features:
- View active, waiting, completed, and failed jobs
- Monitor job progress and details
- Retry failed jobs
- Clean up old jobs

### API Endpoints

**Queue Statistics**:
```
GET http://localhost:3021/api/queue/stats
```

**Job Position**:
```
GET http://localhost:3021/api/queue/position/:jobId
```

## 🔄 How It Works

### 1. Test Execution Flow

```mermaid
sequenceDiagram
    participant Client as WebSocket Client
    participant Server as WebSocket Server
    participant Queue as BullMQ Queue
    participant Worker as Queue Worker
    participant TestRunner as Test Runner

    Client->>Server: execute_test message
    Server->>Queue: Add job to queue
    Queue-->>Server: Job ID & position
    Server-->>Client: test_queued (position info)
    
    Queue->>Worker: Process next job
    Worker->>TestRunner: Execute test
    TestRunner-->>Worker: Test results
    Worker-->>Queue: Job completed
    Queue-->>Client: queue_status updates
```

### 2. Queue Management

- **Concurrency**: Set to 1 (only one test at a time)
- **Retry Logic**: Failed jobs retry up to 3 times with exponential backoff
- **Job Cleanup**: Keeps last 10 completed jobs, 20 failed jobs
- **Priority**: Newer jobs get higher priority

### 3. WebSocket Messages

**New Message Types**:

```typescript
// When test is queued
{
  type: 'test_queued',
  jobId: string,
  position: number,
  queueStats: {
    waiting: number,
    active: number,
    completed: number,
    failed: number
  },
  message: string
}

// Queue status updates
{
  type: 'queue_status',
  status: 'waiting' | 'active' | 'completed' | 'failed',
  jobId: string,
  apiKey: string,
  timestamp: number,
  error?: string
}
```

## 🔧 Configuration

### Queue Options
Located in `src/services/queue-service.ts`:

```typescript
defaultJobOptions: {
  removeOnComplete: 10,    // Keep last 10 completed jobs
  removeOnFail: 20,        // Keep last 20 failed jobs
  attempts: 3,             // Retry failed jobs 3 times
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
}
```

### Worker Options
```typescript
{
  concurrency: 1,          // Only 1 job at a time
  removeOnComplete: 10,
  removeOnFail: 20,
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Redis Connection Error**:
   - Ensure Redis is running: `npm run redis:start`
   - Check Redis connection: `redis-cli ping`

2. **Queue Not Processing**:
   - Check Bull Board dashboard for stuck jobs
   - Restart the server to reinitialize workers

3. **Jobs Failing**:
   - Check job details in Bull Board dashboard
   - Review server logs for error details

### Debugging

**Check Queue Status**:
```bash
curl http://localhost:3021/api/queue/stats
```

**Redis CLI**:
```bash
redis-cli
> KEYS bull:test-execution:*
> LLEN bull:test-execution:waiting
```

## 📈 Benefits

1. **No More Conflicts**: Tests run sequentially, preventing conflicts
2. **Better UX**: Users see their position in queue
3. **Monitoring**: Full visibility into test execution pipeline
4. **Reliability**: Automatic retries and error handling
5. **Scalability**: Easy to adjust concurrency or add more workers

## 🔮 Future Enhancements

- **Priority Queues**: VIP users get higher priority
- **Multiple Workers**: Scale to multiple test runners
- **Job Scheduling**: Schedule tests for specific times
- **Metrics**: Detailed analytics and reporting
