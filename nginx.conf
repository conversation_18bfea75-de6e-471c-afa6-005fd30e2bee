upstream websocket_ai_single_automation_test_agentq_production {
  server 127.0.0.1:3021;
}

server {
  listen 80;
  server_name websocket-ai-single-test-run.agentq.id;
  root /var/www/websocket_ai_single_test/;
  try_files $uri/index.html $uri @websocket_ai_single_automation_test_agentq_production;

  client_max_body_size 4G;
  keepalive_timeout 10;

  error_page 500 502 504 /500.html;
  error_page 503 @503;

  location @websocket_ai_single_automation_test_agentq_production {
    proxy_http_version 1.1;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Host $host;
    proxy_redirect off;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
      proxy_set_header X-Forwarded-Proto http;
      proxy_pass http://websocket_ai_single_automation_test_agentq_production;
    # limit_req zone=one;
    # access_log /home/<USER>/apps/dora_teams/shared/log/nginx.access.log;
    # error_log /home/<USER>/apps/dora_teams/shared/log/nginx.error.log;
  }

  location ^~ /assets/ {
    gzip_static on;
    expires max;
    add_header Cache-Control public;
  }

  location = /50x.html {
    root html;
  }

  location = /404.html {
    root html;
  }

  location @503 {
    error_page 405 = /system/maintenance.html;
    if (-f $document_root/system/maintenance.html) {
      rewrite ^(.*)$ /system/maintenance.html break;
    }
    rewrite ^(.*)$ /503.html break;
  }

  if ($request_method !~ ^(GET|HEAD|PUT|PATCH|POST|DELETE|OPTIONS)$ ){
    return 405;
  }

  if (-f $document_root/system/maintenance.html) {
    return 503;
  }
}
